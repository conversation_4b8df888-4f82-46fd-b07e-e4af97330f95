package cn.iocoder.yudao.module.infra.controller.admin.proxy;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 代理控制器 - 用于解决跨域iframe嵌入问题
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 代理服务")
@RestController
@RequestMapping("/admin-api/infra/proxy")
@Slf4j
public class ProxyController {

    private final RestTemplate restTemplate = new RestTemplate();
    
    private static final String TARGET_HOST = "http://***************:19003";

    @Operation(summary = "代理GET请求")
    @GetMapping("/**")
    public void proxyGet(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String path = extractPath(request);
        String targetUrl = TARGET_HOST + path;
        
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            copyHeaders(request, headers);
            
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                targetUrl, HttpMethod.GET, entity, String.class);
            
            // 复制响应
            copyResponse(responseEntity, response);
            
        } catch (Exception e) {
            log.error("代理GET请求失败: {}", targetUrl, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("代理请求失败: " + e.getMessage());
        }
    }

    @Operation(summary = "代理POST请求")
    @PostMapping("/**")
    public void proxyPost(@RequestBody(required = false) String body,
                         HttpServletRequest request, HttpServletResponse response) throws IOException {
        String path = extractPath(request);
        String targetUrl = TARGET_HOST + path;
        
        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            copyHeaders(request, headers);
            
            HttpEntity<String> entity = new HttpEntity<>(body, headers);
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                targetUrl, HttpMethod.POST, entity, String.class);
            
            // 复制响应
            copyResponse(responseEntity, response);
            
        } catch (Exception e) {
            log.error("代理POST请求失败: {}", targetUrl, e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("代理请求失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取目标网站首页")
    @GetMapping("/page")
    public CommonResult<String> getTargetPage() {
        try {
            String html = restTemplate.getForObject(TARGET_HOST, String.class);
            // 修改HTML中的相对路径为代理路径
            if (html != null) {
                html = html.replaceAll("href=\"/", "href=\"/admin-api/infra/proxy/")
                          .replaceAll("src=\"/", "src=\"/admin-api/infra/proxy/")
                          .replaceAll("action=\"/", "action=\"/admin-api/infra/proxy/");
            }
            return success(html);
        } catch (Exception e) {
            log.error("获取目标页面失败", e);
            return CommonResult.error(500, "获取页面失败: " + e.getMessage());
        }
    }

    private String extractPath(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        String contextPath = "/admin-api/infra/proxy";
        return requestURI.substring(contextPath.length());
    }

    private void copyHeaders(HttpServletRequest request, HttpHeaders headers) {
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            // 跳过一些不需要的头部
            if (shouldSkipHeader(headerName)) {
                continue;
            }
            String headerValue = request.getHeader(headerName);
            headers.add(headerName, headerValue);
        }
        
        // 添加必要的头部
        headers.set("X-Forwarded-For", request.getRemoteAddr());
        headers.set("X-Real-IP", request.getRemoteAddr());
    }

    private boolean shouldSkipHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("host") || 
               lowerName.equals("content-length") ||
               lowerName.equals("authorization") ||
               lowerName.startsWith("x-forwarded");
    }

    private void copyResponse(ResponseEntity<String> responseEntity, HttpServletResponse response) throws IOException {
        // 设置状态码
        response.setStatus(responseEntity.getStatusCode().value());
        
        // 复制响应头
        HttpHeaders responseHeaders = responseEntity.getHeaders();
        for (String headerName : responseHeaders.keySet()) {
            if (!shouldSkipResponseHeader(headerName)) {
                for (String headerValue : responseHeaders.get(headerName)) {
                    response.addHeader(headerName, headerValue);
                }
            }
        }
        
        // 设置响应体
        String body = responseEntity.getBody();
        if (body != null) {
            response.getWriter().write(body);
        }
    }

    private boolean shouldSkipResponseHeader(String headerName) {
        String lowerName = headerName.toLowerCase();
        return lowerName.equals("transfer-encoding") ||
               lowerName.equals("content-encoding") ||
               lowerName.equals("x-frame-options") ||
               lowerName.equals("content-security-policy");
    }
}
