<template>
  <ContentWrap :bodyStyle="{ padding: '0px' }" class="!mb-0">
    <IFrame v-if="!loading" v-loading="loading" :src="src" />
  </ContentWrap>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ToolsAnnotation' })

const loading = ref(true) // 是否加载中
const src = ref('http://***************:19003') // 目标网址

/** 初始化 */
onMounted(async () => {
  // 模拟加载延迟，确保页面正常显示
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>
