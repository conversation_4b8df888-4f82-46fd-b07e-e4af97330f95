<template>
  <ContentWrap :bodyStyle="{ padding: '0px' }" class="!mb-0">
    <div class="iframe-container">
      <iframe
        ref="frameRef"
        :src="src"
        frameborder="0"
        scrolling="auto"
        width="100%"
        height="100%"
        allowfullscreen="true"
        webkitallowfullscreen="true"
        mozallowfullscreen="true"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
        @load="onFrameLoad"
      ></iframe>
      <div v-if="loading" class="loading-overlay">
        <el-loading-spinner />
        <span>正在加载...</span>
      </div>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ToolsAnnotation' })

const loading = ref(true)
const frameRef = ref<HTMLIFrameElement>()
// 方案1: 直接访问（可能有CSRF问题）
// const src = ref('http://***************:19003')

// 方案2: 使用后端代理（推荐）
const src = ref('/admin-api/infra/proxy/page')

const onFrameLoad = () => {
  loading.value = false

  // 尝试与iframe通信，处理CSRF问题
  try {
    const iframe = frameRef.value
    if (iframe && iframe.contentWindow) {
      // 监听iframe内的消息
      window.addEventListener('message', handleIframeMessage, false)
    }
  } catch (error) {
    console.warn('无法访问iframe内容，可能是跨域限制:', error)
  }
}

const handleIframeMessage = (event: MessageEvent) => {
  // 验证消息来源
  if (event.origin !== 'http://***************:19003') {
    return
  }

  // 处理来自iframe的消息
  console.log('收到iframe消息:', event.data)
}

onMounted(() => {
  // 设置较长的加载超时
  setTimeout(() => {
    if (loading.value) {
      loading.value = false
    }
  }, 10000)
})

onUnmounted(() => {
  window.removeEventListener('message', handleIframeMessage, false)
})
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  height: calc(100vh - var(--top-tool-height) - var(--tags-view-height) - var(--app-content-padding) - var(--app-content-padding) - 2px);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-overlay span {
  margin-top: 10px;
  color: #666;
}
</style>
